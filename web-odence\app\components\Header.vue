<script setup>
// import { ChevronDownIcon } from '@heroicons/vue/24/solid';
// import { ChevronDoubleDownIcon } from '@heroicons/vue/24/solid';

const { scrollToElement } = useScroll();
let colorMode = {
	value: 'dark'
}

const logo = computed(() => {
	return colorMode.value === 'dark' ? 'Game-1-Logo-1-Dark.png?updatedAt=1759417763418' : 'Game-1-Logo-1-Light.png?updatedAt=1759417792592'
});

onMounted(() => {
	colorMode = useColorMode();
	document.documentElement.setAttribute('data-theme', colorMode.value);
	document.documentElement.classList.toggle('dark', colorMode.value === 'dark');
});

</script>

<template>
	<nav class="navbar inset-x-0 bg-base-300/90 fixed top-0 z-50 px-6 sm:px-9 md:px-12 lg:px-15 py-2 max-xs:grid backdrop-blur-sm" :class="(colorMode.value === 'dark') ? 'shadow-lg' : 'shadow-md'">
		<!-- Logo -->
		<div class="flex items-center justify-self-center">
			<div class="flex items-center justify-center sm:ms-3">
				<ClientOnly>
					<a href="#hero" @click.prevent="scrollToElement('hero')">
						<div class="p-1.5">
							<NuxtPicture provider="imagekit" :src="logo" alt="Logo" :imgAttrs="{ class: 'max-h-9' }" />
						</div>
					</a>
					<Theme />
				</ClientOnly>
			</div>

			<!-- <div class="dropdown">
				<div tabindex="0" aria-label="Menu" role="button" class="swap swap-active me-2 focus:-rotate-180 lg:hidden duration-200 ease-out transition-all hover:-translate-y-0.5 hover:text-orange-700 hover:dark:text-orange-200">
					<ChevronDoubleDownIcon aria-hidden="true" aria-label="Menu Icon" class="swap-on size-6" />
				</div>
				<ul tabindex="0" aria-label="Menu List" role="menu" class="menu dropdown-content bg-base-100 rounded-box z-1 w-52 mt-6 shadow">
					<li class="focus:bg-transparent">
						<a href="#hero" @click.prevent="scrollToElement('hero')" class="font-medium text-orange-700 dark:text-orange-200" >{{ $t('menu.overview.name') }}</a>
						<ul class="p-2">
							<li><a href="#about" @click.prevent="scrollToElement('about')">{{ $t('menuAbout') }}</a></li>
							<li><a href="#faq" @click.prevent="scrollToElement('faq')">{{ $t('faqTitle') }}</a></li>
							<li><a href="#contact" @click.prevent="scrollToElement('contact')">{{ $t('menuContact') }}</a></li>
						</ul>
					</li>
					<li>
						<a href="#projects" @click.prevent="scrollToElement('projects')" class="font-medium text-orange-700 dark:text-orange-200" >{{ $t('menuProjects') }}</a>
						<ul class="p-2">
							<li><a href="#game-1" @click.prevent="scrollToElement('game-1', -72)">{{ $t('menuGames1') }}</a></li>
							<li><a href="#library-1" @click.prevent="scrollToElement('library-1', -72)">{{ $t('menuLibs1') }}</a></li>
						</ul>
					</li>

					<li class="xs:hidden">
						<div class="border-t-3 dark:border-base-200 border-base-300 mt-2 flex items-center ">
							<ThemeSwitch :classes="'flex-none'" />
							<LocaleSwitch :type="'button-mobile'" :dropdownClass="'grow justify-end'" :itemClass="'-ms-12 mt-15'" />
						</div>
					</li>
				</ul>
			</div>

			<div class="flex items-center justify-center ms-3 ">
				<a href="#hero" @click.prevent="scrollToElement('hero')" class="bg-gray-900/85 rounded-xl dark:bg-transparent dark:rounded-none ">
					<div class="p-1.5">
						<NuxtPicture provider="imagekit" src="Logo.png?updatedAt=1745537280301" alt="Logo" :imgAttrs="{ class: 'size-9' }" />
					</div>
				</a>
				<p class="font-semibold dark:font-medium text-2xl ms-4 tracking-wider font-anta">ODD SEQUENCE</p>
			</div> -->
		</div>
	</nav>
</template>
